<body onload="initForm();" onfocus="onFocusForm();" >

    <jsp:include page="header.jsp">
		<jsp:param name="PARAM" value="特売実績照会（TNP03003）" />
    </jsp:include>
    <html:form action="tokubaiJisekiReference" method="post" style="width: 995px; margin: 0px auto;">
    <fvo:text style="display: none;" property='tenpo_cd' />
    <br />
    <!-- 検索条件　開始 -->
    <div align="left" style="width: 709px; height: 26px; display: block; margin-left: auto; margin-right: auto;">
      <!-- 50%にするとブラウザをドラッグしてリサイズした時にレイアウトが崩れるので、49.99%にしています -->
      <fvo:span style="" property='bunrui1_area' >
        <table class="bunrui" style="width: 709px;">
          <tr height="25px">
            <th style="width: 106px; box-sizing: border-box; border-left-style: solid; border-top-style: solid;">商品</th>
            <td style="width: 141px; box-sizing: border-box; border-left-style: solid; border-top-style: solid; padding: 1px;"><fvo:span property='syohin_cd' ></fvo:span></td>
            <td style="width: 460px; box-sizing: border-box; border-left-style: solid; border-top-style: solid; padding: 1px;"><fvo:span property='syohin_na' ></fvo:span></td>
          </tr>
        </table>
      </fvo:span>
    </div>
    <!-- 検索条件　終了 -->

    <div style="height: 28px; display: block;">
    <table align="center">
      <tr>
        <td align="left" style="height: 24px;">
          <fvo:span property='message' ></fvo:span>
        </td>
      </tr>
    </table></div>

    <!-- 検索結果　開始（"ヘッダ部と明細部の左端揃え"と"divの幅を抑える"為に空のtableで囲む） -->
    <fvo:span style="" property='result_area' >
            <!-- 検索結果：ヘッダ　開始 -->
      <table align="center" >
        <tr>
          <td align="left">
            <table class="list" style="">
              <thead>
                <tr>
                  <th style="width: 320px; padding: 1px;" valign="top">
                    <table class="head" width="100%" style="text-align: left; ">
                      <tr>
                        <th width="40%"><label>テーマコード</label></th>
                        <th width="60%"><label>特売区分</label></th>
                      </tr>
                    </table>
                    <table class="head" width="100%" style="text-align: left; ">
                      <tr>
                        <th width="100%"><label>テーマ名</label></th>
                      </tr>
                    </table>
                    <table class="head" width="100%" style="text-align: left; ">
                      <tr>
                        <th width="30%"><label>原単価</label></th>
                        <th width="30%"><label>売単価</label></th>
                        <th width="30%"><label></label></th>
                      </tr>
                    </table>
                    <table class="head" width="100%" style="text-align: left; ">
                      <tr>
                        <th width="100%"><label>特売販売期間</label></th>
                      </tr>
                    </table>
                  </th>
                  <th style="width: 511px; padding: 1px; text-align: center;">特売実績</th>
                </tr>
              </thead>
            </table>
            <!-- 検索結果：ヘッダ　終了 -->

            <!-- 検索結果：明細　開始 -->
            <div id="result_scroll_area" class="scroll" style="height: 320px;">
              <table class="list">
                <logic:iterate name="TokubaiJisekiReferenceForm" property='tokubai_result_rows' id="tokubai_result_rows" indexId="tokubai_result_rowsindex" type="jp.co.vinculumjapan.mdware.ordercommon.struts.form.rows.TokubaiJisekiReferenceFormTokubai_result_rowsBean" >
				<tr height="25" style="line-height: 1.3em;">
                  <td valign="top" rowspan="" style="width: 320px; padding: 1px;">
                    <table class="head" width="100%">
                      <tr>
                        <td width="40%">
                          <label class="bold" style="font-size: 1em;">
                            <fvo:span property='<%="tokubai_result_rows[" + tokubai_result_rowsindex + "].theme_cd"%>' ></fvo:span>
                          </label>
                        </td>
                        <td width="60%">
                          <label class="bold" style="font-size: 1em;">
                            <fvo:span property='<%="tokubai_result_rows[" + tokubai_result_rowsindex + "].tokubai_na"%>' ></fvo:span>
                          </label>
                        </td>
                      </tr>
                    </table>
                    <table class="head" width="100%">
                      <tr>
                        <td width="100%">
                          <label class="bold" style="font-size: 1em;">
                            <fvo:span style="display:inline-block; width:319px; overflow:hidden;" property='<%="tokubai_result_rows[" + tokubai_result_rowsindex + "].theme_na"%>' ></fvo:span>
                          </label>
                        </td>
                      </tr>
                    </table>
                    <table class="head" width="100%">
                      <tr>
                        <td width="20%" class="num">
                          <label class="bold"style="font-size: 1em;">
                            <fvo:span property='<%="tokubai_result_rows[" + tokubai_result_rowsindex + "].tokubai_genka_vl"%>' ></fvo:span>
                          </label>
                        </td>
                        <td width="20%" class="num">
                          <label class="bold" style="font-size: 1em;">
                            <fvo:span property='<%="tokubai_result_rows[" + tokubai_result_rowsindex + "].tokubai_tanka_vl"%>' ></fvo:span>
                          </label>
                        </td>
                        <td width="60%" class="num">
                          <label class="bold" style="font-size: 1em;">
                          </label>
                        </td>
                      </tr>
                    </table>
                    <table class="head" width="100%">
                      <tr>
                        <td width="40%" class="num">
                          <label class="bold" style="font-size: 1em;">
                            <fvo:span property='<%="tokubai_result_rows[" + tokubai_result_rowsindex + "].hanbai_start_dt"%>' ></fvo:span>
                            <fvo:span property='<%="tokubai_result_rows[" + tokubai_result_rowsindex + "].haihun"%>' ></fvo:span>
                            <fvo:span property='<%="tokubai_result_rows[" + tokubai_result_rowsindex + "].hanbai_end_dt"%>' ></fvo:span>
                          </label>
                        <td width="60%" class="num">
                          <label class="bold" style="font-size: 1em;">
                          </label>
                        </td>
                      </tr>
                    </table>
                  </td>

                  <td style="width: 511px; padding: 1px; ">
                    <table class="list" id="right-result" style="width: 511px; display: block; ">
                      <tr>
                        <th nowrap style="width :89px; border-left: none; box-sizing: border-box;">日付</th>
                        <logic:iterate name="tokubai_result_rows" property='nohin_cols' id="nohin_cols" indexId="nohin_colsindex" type="jp.co.vinculumjapan.mdware.ordercommon.struts.form.rows.TokubaiJisekiReferenceFormTokubai_result_rowsNohin_colsBean" >
						<td style="width :60px; text-align:center; border-right: none;" nowrap >
                          <fvo:span property='<%="tokubai_result_rows[" + tokubai_result_rowsindex + "].nohin_cols[" + nohin_colsindex + "].nohin_dt"%>' ></fvo:span>
                          <fvo:span property='<%="tokubai_result_rows[" + tokubai_result_rowsindex + "].nohin_cols[" + nohin_colsindex + "].yobi_na"%>' ></fvo:span>
                        </td>
						</logic:iterate>
                      </tr>
                      <tr style="height: 75px;">
                        <th style="border-style: none;">
                          <span style="width: 100%; height: 18px; display: block;">納品数量</span>
                          <span style="width: 100%; height: 18px; display: block;">売上数量</span>
                          <span style="width: 100%; height: 18px; display: block;">値下数量</span>
                          <span style="width: 100%; height: 18px; display: block;">廃棄数量</span>
                        </th>
                        <logic:iterate name="tokubai_result_rows" property='suryo_cols' id="suryo_cols" indexId="suryo_colsindex" type="jp.co.vinculumjapan.mdware.ordercommon.struts.form.rows.TokubaiJisekiReferenceFormTokubai_result_rowsSuryo_colsBean" >
						<td class="numeric_bold" style="border-bottom-style: none; text-align:right; text-indent: 0; border-right: none;">
                          <fvo:span property='<%="tokubai_result_rows[" + tokubai_result_rowsindex + "].suryo_cols[" + suryo_colsindex + "].nohin_suryo_qt"%>' ></fvo:span>
                          <br />
                          <fvo:span property='<%="tokubai_result_rows[" + tokubai_result_rowsindex + "].suryo_cols[" + suryo_colsindex + "].uriage_suryo_qt"%>' ></fvo:span>
                          <br />
                          <fvo:span property='<%="tokubai_result_rows[" + tokubai_result_rowsindex + "].suryo_cols[" + suryo_colsindex + "].nebiki_suryo_qt"%>' ></fvo:span>
                          <br />
                          <fvo:span property='<%="tokubai_result_rows[" + tokubai_result_rowsindex + "].suryo_cols[" + suryo_colsindex + "].haiki_suryo_qt"%>' ></fvo:span>
                        </td>
						</logic:iterate>
                      </tr>
                    </table>
                  </td>
                </tr>
				</logic:iterate>
              </table>
            </div>
            <!-- 検索結果：明細　終了 -->

          </td>
        </tr>
      </table>
    </fvo:span>

    <table width="100%">
      <tr>
      <td width="1%"></td>
      <td width="*" align="center">
    	<fvo:button value="&emsp;戻&emsp;る&emsp;" onclick="javascript:window.close();" property='closewindow' styleClass="controlButton" />
      </td>
      <td width="1%" align="right">
      </tr>
    </table>

    <jsp:include page="footer.jsp" />

</html:form></body>