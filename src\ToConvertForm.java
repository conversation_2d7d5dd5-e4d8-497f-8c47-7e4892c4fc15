// ToConvertForm.java - Form class for the converted JSP
import java.util.List;

public class ToConvertForm {
    // Hidden fields
    private String online_dt;
    private String pre_method_name;
    
    // Search term fields
    private String term_itemcd;
    private String term_itemnm;
    private String term_dept_area;
    private String term_dptcd;
    private String term_dept_na;
    private String term_dept_sub_button;
    private String term_dept_sub_clear;
    
    // Control buttons
    private String search;
    private String back;
    private String close;
    
    // Message area
    private String message;
    
    // Result areas
    private String term_result_area;
    private String term_info_item_area;
    private String term_item_cd;
    private String term_item_nm;
    
    // List2 area fields - updated with term_ prefix for JSP property mapping
    private String list2_area;
    private String list2_dptcd;
    private String list2_tananb;
    private String list2_tanadannb;
    private String list2_seq;
    private String list2_date;

    // Additional term_ prefixed properties for new JSP structure (avoiding duplicates)
    private String term_tananb;
    private String term_tanadannb;
    private String term_seq;
    private String term_date;
    
    // Detail area
    private String meisai_area;
    private String meisai_header;
    private List result_rows;

    /**
     * online_dt ???????
     * @return online_dt
     */
    public String getOnline_dt() {
        return online_dt;
    }

    /**
     * online_dt ???????
     * @param online_dt
     */
    public void setOnline_dt(String online_dt) {
        this.online_dt = online_dt;
    }

    /**
     * pre_method_name ???????
     * @return pre_method_name
     */
    public String getPre_method_name() {
        return pre_method_name;
    }

    /**
     * pre_method_name ???????
     * @param pre_method_name
     */
    public void setPre_method_name(String pre_method_name) {
        this.pre_method_name = pre_method_name;
    }

    /**
     * term_itemcd ???????
     * @return term_itemcd
     */
    public String getTerm_itemcd() {
        return term_itemcd;
    }

    /**
     * term_itemcd ???????
     * @param term_itemcd
     */
    public void setTerm_itemcd(String term_itemcd) {
        this.term_itemcd = term_itemcd;
    }

    /**
     * term_itemnm ???????
     * @return term_itemnm
     */
    public String getTerm_itemnm() {
        return term_itemnm;
    }

    /**
     * term_itemnm ???????
     * @param term_itemnm
     */
    public void setTerm_itemnm(String term_itemnm) {
        this.term_itemnm = term_itemnm;
    }

    /**
     * term_dept_area ???????
     * @return term_dept_area
     */
    public String getTerm_dept_area() {
        return term_dept_area;
    }

    /**
     * term_dept_area ???????
     * @param term_dept_area
     */
    public void setTerm_dept_area(String term_dept_area) {
        this.term_dept_area = term_dept_area;
    }

    /**
     * term_dptcd ???????
     * @return term_dptcd
     */
    public String getTerm_dptcd() {
        return term_dptcd;
    }

    /**
     * term_dptcd ???????
     * @param term_dptcd
     */
    public void setTerm_dptcd(String term_dptcd) {
        this.term_dptcd = term_dptcd;
    }

    /**
     * term_dept_na ???????
     * @return term_dept_na
     */
    public String getTerm_dept_na() {
        return term_dept_na;
    }

    /**
     * term_dept_na ???????
     * @param term_dept_na
     */
    public void setTerm_dept_na(String term_dept_na) {
        this.term_dept_na = term_dept_na;
    }

    /**
     * term_dept_sub_button ???????
     * @return term_dept_sub_button
     */
    public String getTerm_dept_sub_button() {
        return term_dept_sub_button;
    }

    /**
     * term_dept_sub_button ???????
     * @param term_dept_sub_button
     */
    public void setTerm_dept_sub_button(String term_dept_sub_button) {
        this.term_dept_sub_button = term_dept_sub_button;
    }

    /**
     * term_dept_sub_clear ???????
     * @return term_dept_sub_clear
     */
    public String getTerm_dept_sub_clear() {
        return term_dept_sub_clear;
    }

    /**
     * term_dept_sub_clear ???????
     * @param term_dept_sub_clear
     */
    public void setTerm_dept_sub_clear(String term_dept_sub_clear) {
        this.term_dept_sub_clear = term_dept_sub_clear;
    }

    /**
     * search ???????
     * @return search
     */
    public String getSearch() {
        return search;
    }

    /**
     * search ???????
     * @param search
     */
    public void setSearch(String search) {
        this.search = search;
    }

    /**
     * back ???????
     * @return back
     */
    public String getBack() {
        return back;
    }

    /**
     * back ???????
     * @param back
     */
    public void setBack(String back) {
        this.back = back;
    }

    /**
     * close ???????
     * @return close
     */
    public String getClose() {
        return close;
    }

    /**
     * close ???????
     * @param close
     */
    public void setClose(String close) {
        this.close = close;
    }

    /**
     * message ???????
     * @return message
     */
    public String getMessage() {
        return message;
    }

    /**
     * message ???????
     * @param message
     */
    public void setMessage(String message) {
        this.message = message;
    }

    /**
     * term_result_area ???????
     * @return term_result_area
     */
    public String getTerm_result_area() {
        return term_result_area;
    }

    /**
     * term_result_area ???????
     * @param term_result_area
     */
    public void setTerm_result_area(String term_result_area) {
        this.term_result_area = term_result_area;
    }

    /**
     * term_info_item_area ???????
     * @return term_info_item_area
     */
    public String getTerm_info_item_area() {
        return term_info_item_area;
    }

    /**
     * term_info_item_area ???????
     * @param term_info_item_area
     */
    public void setTerm_info_item_area(String term_info_item_area) {
        this.term_info_item_area = term_info_item_area;
    }

    /**
     * term_item_cd ???????
     * @return term_item_cd
     */
    public String getTerm_item_cd() {
        return term_item_cd;
    }

    /**
     * term_item_cd ???????
     * @param term_item_cd
     */
    public void setTerm_item_cd(String term_item_cd) {
        this.term_item_cd = term_item_cd;
    }

    /**
     * term_item_nm ???????
     * @return term_item_nm
     */
    public String getTerm_item_nm() {
        return term_item_nm;
    }

    /**
     * term_item_nm ???????
     * @param term_item_nm
     */
    public void setTerm_item_nm(String term_item_nm) {
        this.term_item_nm = term_item_nm;
    }

    /**
     * list2_area ???????
     * @return list2_area
     */
    public String getList2_area() {
        return list2_area;
    }

    /**
     * list2_area ???????
     * @param list2_area
     */
    public void setList2_area(String list2_area) {
        this.list2_area = list2_area;
    }

    /**
     * list2_dptcd ???????
     * @return list2_dptcd
     */
    public String getList2_dptcd() {
        return list2_dptcd;
    }

    /**
     * list2_dptcd ???????
     * @param list2_dptcd
     */
    public void setList2_dptcd(String list2_dptcd) {
        this.list2_dptcd = list2_dptcd;
    }

    /**
     * list2_tananb ???????
     * @return list2_tananb
     */
    public String getList2_tananb() {
        return list2_tananb;
    }

    /**
     * list2_tananb ???????
     * @param list2_tananb
     */
    public void setList2_tananb(String list2_tananb) {
        this.list2_tananb = list2_tananb;
    }

    /**
     * list2_tanadannb ???????
     * @return list2_tanadannb
     */
    public String getList2_tanadannb() {
        return list2_tanadannb;
    }

    /**
     * list2_tanadannb ???????
     * @param list2_tanadannb
     */
    public void setList2_tanadannb(String list2_tanadannb) {
        this.list2_tanadannb = list2_tanadannb;
    }

    /**
     * list2_seq ???????
     * @return list2_seq
     */
    public String getList2_seq() {
        return list2_seq;
    }

    /**
     * list2_seq ???????
     * @param list2_seq
     */
    public void setList2_seq(String list2_seq) {
        this.list2_seq = list2_seq;
    }

    /**
     * list2_date ???????
     * @return list2_date
     */
    public String getList2_date() {
        return list2_date;
    }

    /**
     * list2_date ???????
     * @param list2_date
     */
    public void setList2_date(String list2_date) {
        this.list2_date = list2_date;
    }

    /**
     * meisai_area ???????
     * @return meisai_area
     */
    public String getMeisai_area() {
        return meisai_area;
    }

    /**
     * meisai_area ???????
     * @param meisai_area
     */
    public void setMeisai_area(String meisai_area) {
        this.meisai_area = meisai_area;
    }

    /**
     * meisai_header ???????
     * @return meisai_header
     */
    public String getMeisai_header() {
        return meisai_header;
    }

    /**
     * meisai_header ???????
     * @param meisai_header
     */
    public void setMeisai_header(String meisai_header) {
        this.meisai_header = meisai_header;
    }

    /**
     * result_rows ???????
     * @return result_rows
     */
    public List getResult_rows() {
        return result_rows;
    }

    /**
     * result_rows ???????
     * @param result_rows
     */
    public void setResult_rows(List result_rows) {
        this.result_rows = result_rows;
    }

    /**
     * term_tananb ???????
     * @return term_tananb
     */
    public String getTerm_tananb() {
        return term_tananb;
    }

    /**
     * term_tananb ???????
     * @param term_tananb
     */
    public void setTerm_tananb(String term_tananb) {
        this.term_tananb = term_tananb;
    }

    /**
     * term_tanadannb ???????
     * @return term_tanadannb
     */
    public String getTerm_tanadannb() {
        return term_tanadannb;
    }

    /**
     * term_tanadannb ???????
     * @param term_tanadannb
     */
    public void setTerm_tanadannb(String term_tanadannb) {
        this.term_tanadannb = term_tanadannb;
    }

    /**
     * term_seq ???????
     * @return term_seq
     */
    public String getTerm_seq() {
        return term_seq;
    }

    /**
     * term_seq ???????
     * @param term_seq
     */
    public void setTerm_seq(String term_seq) {
        this.term_seq = term_seq;
    }

    /**
     * term_date ???????
     * @return term_date
     */
    public String getTerm_date() {
        return term_date;
    }

    /**
     * term_date ???????
     * @param term_date
     */
    public void setTerm_date(String term_date) {
        this.term_date = term_date;
    }
}
